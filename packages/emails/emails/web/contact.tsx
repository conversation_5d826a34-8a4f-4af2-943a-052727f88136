import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { z } from 'zod';

export const contactFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Invalid email address.' }),
  phone: z.string().min(10, { message: 'Please enter a valid phone number.' }),
  reason: z.string().min(1, { message: 'Please select a reason for contact.' }),
  message: z
    .string()
    .min(10, { message: 'Please enter a message (at least 10 characters).' }),
});

type ContactProps = z.infer<typeof contactFormSchema>;

const baseUrl = 'https://www.thehuefactory.co/';

const colors = {
  '100': '#ff4200',
  '200': '#d53700',
  '300': '#7f2100',
  '400': '#3e1000',
};

export const ContactClient = ({ name, reason }: ContactProps) => (
  <Html>
    <Head />
    <Preview>Your Message Has Been Received</Preview>
    <Body style={main}>
      <Container
        style={{
          ...container,
          backgroundColor: colors['100'],
        }}
      >
        <Img
          alt='Email Header Image'
          height='auto'
          src={`${baseUrl}/thehuefactory_hero.png`}
          width='100%'
        />
      </Container>
      <Container
        style={{
          margin: '0 auto',
          backgroundColor: colors['100'],
          alignItems: 'center',
          alignContent: 'center',
          textAlign: 'center',
        }}
      >
        <Section
          style={{
            backgroundColor: 'white',
            height: 20,
            borderTopLeftRadius: '16px',
            borderTopRightRadius: '16px',
          }}
        />
      </Container>
      <Container style={container}>
        <Heading style={h1}>Hey {name}, </Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          Thank you for reaching out to us regarding{' '}
          <span style={{ fontWeight: 'bold' }}>{reason}</span>. <br />
          We have received your message and will get back to you within 24-48 hours.
        </Text>
        <Text style={{ ...text, marginBottom: '24px' }}>
          If you have any urgent questions, feel free to{' '}
          <Link
            href='mailto:<EMAIL>'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            email us directly
          </Link>{' '}
          or visit our{' '}
          <Link
            href='https://www.thehuefactory.co/contact'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            contact page
          </Link>.
        </Text>
      </Container>
      <Container
        style={{
          ...container,
          marginTop: '48px',
        }}
      >
        <Img
          alt="thehuefactory's Logo"
          height='42'
          src={`${baseUrl}/Logo_3dicon_orange.png`}
          width='42'
        />
        <Text style={{ ...footer, marginTop: '40px' }}>
          <Link
            href='https://www.thehuefactory.co/'
            style={{ ...link, color: colors[100], fontWeight: 'bold' }}
            target='_blank'
          >
            thehuefactory.co
          </Link>{' '}
          <br />
          The Creative Powerhouse.
          <br />
          Copyright © 2025 thehuefactory. All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

export const ContactAdmin = ({ name, email, phone, reason, message }: ContactProps) => (
  <Html>
    <Head />
    <Preview>New Contact Form Submission from {name}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>New Contact Form Submission</Heading>
        <Text style={{ ...text, marginBottom: '24px' }}>
          You have received a new contact form submission:
        </Text>
        
        <Section style={infoSection}>
          <Text style={infoLabel}>Name:</Text>
          <Text style={infoValue}>{name}</Text>
        </Section>
        
        <Section style={infoSection}>
          <Text style={infoLabel}>Email:</Text>
          <Text style={infoValue}>{email}</Text>
        </Section>
        
        <Section style={infoSection}>
          <Text style={infoLabel}>Phone:</Text>
          <Text style={infoValue}>{phone}</Text>
        </Section>
        
        <Section style={infoSection}>
          <Text style={infoLabel}>Reason for Contact:</Text>
          <Text style={infoValue}>{reason}</Text>
        </Section>
        
        <Section style={infoSection}>
          <Text style={infoLabel}>Message:</Text>
          <Text style={messageValue}>{message}</Text>
        </Section>
        
        <Text style={{ ...text, marginTop: '24px' }}>
          Please respond to this inquiry within 24-48 hours.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default ContactClient;

const main = {
  backgroundColor: '#ffffff',
};

const container = {
  paddingLeft: '12px',
  paddingRight: '12px',
  margin: '0 auto',
};

const h1 = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0',
  padding: '0',
};

const link = {
  color: '#2754C5',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  textDecoration: 'underline',
};

const text = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '24px 0',
};

const footer = {
  color: '#898989',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  lineHeight: '22px',
  marginTop: '12px',
  marginBottom: '24px',
};

const infoSection = {
  marginBottom: '16px',
};

const infoLabel = {
  color: '#666',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '12px',
  fontWeight: 'bold',
  margin: '0 0 4px 0',
};

const infoValue = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '0 0 8px 0',
};

const messageValue = {
  color: '#333',
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: '14px',
  margin: '0 0 8px 0',
  padding: '12px',
  backgroundColor: '#f5f5f5',
  borderRadius: '4px',
  whiteSpace: 'pre-wrap',
};
