{"$schema": "https://json.schemastore.org/tsconfig", "display": "Next.js", "compilerOptions": {"composite": false, "downlevelIteration": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strictNullChecks": true, "plugins": [{"name": "next"}], "allowJs": true, "declaration": false, "declarationMap": false, "incremental": false, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext", "ESNext.AsyncIterable"], "noEmit": true, "strict": false, "target": "ESNext", "module": "ESNext", "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "types": ["vitest/globals"], "outDir": "dist"}, "include": ["next-env.d.ts", "tailwind-internals.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": [".next", "dist", "node_modules"]}