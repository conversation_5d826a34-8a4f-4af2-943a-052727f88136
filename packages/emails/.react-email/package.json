{"name": "preview-server", "version": "4.0.17", "description": "A live preview of your emails right in your browser.", "bin": {"email": "./dist/cli/index.mjs"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/resend/react-email.git", "directory": "packages/react-email"}, "keywords": ["react", "email"], "engines": {"node": ">=18.0.0"}, "dependencies": {"@babel/parser": "^7.27.0", "@babel/traverse": "^7.27.0", "chalk": "^5.0.0", "chokidar": "^4.0.3", "commander": "^13.0.0", "debounce": "^2.0.0", "esbuild": "^0.25.0", "glob": "^11.0.0", "log-symbols": "^7.0.0", "mime-types": "^3.0.0", "next": "^15.3.1", "normalize-path": "^3.0.0", "ora": "^8.0.0", "socket.io": "^4.8.1", "tsconfig-paths": "4.2.0"}, "devDependencies": {"@babel/core": "7.26.10", "@lottiefiles/dotlottie-react": "0.13.3", "@radix-ui/colors": "3.0.0", "@radix-ui/react-collapsible": "1.1.7", "@radix-ui/react-dropdown-menu": "2.1.10", "@radix-ui/react-popover": "1.1.10", "@radix-ui/react-slot": "1.2.0", "@radix-ui/react-tabs": "1.1.7", "@radix-ui/react-toggle-group": "1.1.6", "@radix-ui/react-tooltip": "1.2.3", "@swc/core": "1.11.21", "@types/babel__core": "7.20.5", "@types/babel__traverse": "7.20.7", "@types/fs-extra": "11.0.1", "@types/mime-types": "2.1.4", "@types/node": "22.14.1", "@types/normalize-path": "3.0.2", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/webpack": "5.28.5", "autoprefixer": "10.4.21", "clsx": "2.1.1", "framer-motion": "12.7.5", "jiti": "2.4.2", "json5": "2.2.3", "module-punycode": "npm:punycode@2.3.1", "node-html-parser": "7.0.1", "postcss": "8.5.3", "pretty-bytes": "6.1.1", "prism-react-renderer": "2.4.1", "react": "19.0.0", "react-dom": "19.0.0", "sharp": "0.34.1", "socket.io-client": "4.8.1", "sonner": "2.0.3", "source-map-js": "1.2.1", "spamc": "0.0.5", "stacktrace-parser": "0.1.11", "tailwind-merge": "3.2.0", "tailwindcss": "3.4.0", "tsup": "8.4.0", "tsx": "4.19.3", "typescript": "5.8.3", "use-debounce": "10.0.4", "zod": "3.24.3"}, "scripts": {"build": "next build", "caniemail:fetch": "node ./scripts/fill-caniemail-data.mjs", "clean": "rm -rf dist", "dev": "tsup-node --watch", "dev:preview": "cd ../../apps/demo && tsx ../../packages/react-email/src/cli/index.ts dev", "test": "vitest run", "test:watch": "vitest", "start": "next start"}}