import * as React from 'react';
import type { IconElement, IconProps } from './icon-base';
import { IconBase } from './icon-base';

export const IconFolderOpen = React.forwardRef<
  IconElement,
  Readonly<IconProps>
>(({ ...props }, forwardedRef) => (
  <IconBase ref={forwardedRef} {...props}>
    <path
      clipRule="evenodd"
      d="M6.75 4C5.23122 4 4 5.23122 4 6.75V17.25V17.5C4 17.5933 4.01702 17.6825 4.04812 17.7649C4.28918 19.0376 5.4072 20 6.75 20H17.25C17.9905 20 18.6283 19.7404 19.111 19.2387C19.5681 18.7636 19.836 18.1241 19.9792 17.4279L21.4711 12.206C21.4903 12.139 21.5 12.0697 21.5 12C21.5 10.652 20.5301 9.53047 19.25 9.29534V8.5C19.25 7.5335 18.4665 6.75 17.5 6.75H13.9452L13.227 5.43322C12.7451 4.54965 11.819 4 10.8127 4H6.75ZM17.75 9.25V8.5C17.75 8.36193 17.6381 8.25 17.5 8.25H13.5C13.2255 8.25 12.973 8.10009 12.8416 7.85915L11.9101 6.15145L11.91 6.15138C11.6911 5.74989 11.2702 5.5 10.8127 5.5H6.75C6.05964 5.5 5.5 6.05964 5.5 6.75V13.158L6.79875 9.73401C6.90926 9.44267 7.1884 9.25 7.5 9.25H17.75ZM5.50587 17.372L8.01766 10.75H18.5H18.75C19.4091 10.75 19.949 11.26 19.9966 11.9069L18.5289 17.044C18.5233 17.0634 18.5185 17.0831 18.5146 17.1029C18.4062 17.6448 18.2275 17.9934 18.03 18.1988C17.8513 18.3846 17.6141 18.5 17.25 18.5H6.75C6.10079 18.5 5.56718 18.0051 5.50587 17.372Z"
      fill="currentColor"
      fillRule="evenodd"
    />
  </IconBase>
));

IconFolderOpen.displayName = 'IconFolderOpen';
