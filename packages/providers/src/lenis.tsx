'use client';

import { ReactLenis as OriginalReactLenis } from 'lenis/react';
import type React from 'react';
import { useEffect, useState } from 'react';

export * from 'lenis/react';

// SSR-safe wrapper for ReactLenis
export function ReactLenis({
  children,
  ...props
}: React.ComponentProps<typeof OriginalReactLenis>) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return <>{children}</>;
  }

  return <OriginalReactLenis {...props}>{children}</OriginalReactLenis>;
}
