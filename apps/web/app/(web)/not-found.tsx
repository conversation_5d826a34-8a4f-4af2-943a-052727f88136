import { FONT_CHAKRA_PETCH } from '@thf/constants/fonts';
import { cn } from '@thf/ui/utils/cn';
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className={cn(FONT_CHAKRA_PETCH.className, 'font-sans')}>
      <div className='flex min-h-dvh w-full flex-col items-center justify-center'>
        <h2 className='mb-4 font-bold text-2xl'>Not Found</h2>
        <p className='mb-6 text-gray-600'>Could not find requested resource</p>
        <Link
          className='rounded bg-blue-500 px-4 py-2 text-white transition-colors hover:bg-blue-600'
          href='/'
        >
          Return Home
        </Link>
      </div>
    </div>
  );
}
